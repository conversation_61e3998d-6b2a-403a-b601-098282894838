CREATE OR REPLACE FUNCTION public.tms_create_or_update_category(form_data_ json, entry_id integer DEFAULT 0)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		resp_data json;
		message text;
		status boolean;

		org_id_ int;
		ins_id integer;
		affected_rows integer;
		usr_id_ uuid;
		ip_address_ text;
		user_agent_ text;
		sku_ text;
		category_name_ text;
		description_ text;
		price_ numeric;
		does_has_serial_no_ bool default false;
		does_has_serial_no_status text;
		is_active_ bool default false;
		is_active_status text;
		validation_resp_ json;

	BEGIN
		message = 'Internal_error';
		status = false;		
	
		usr_id_ = form_data_->>'usr_id';
		org_id_ = form_data_->'org_id';
	
		
		category_name_ = form_data_->>'category_name';
		
		ip_address_ = form_data_->>'ip_address';
		user_agent_ = form_data_->>'user_agent';
		is_active_status = form_data_->>'is_active';
		
	
		if is_active_status = 'true' or UPPER(is_active_status) = 'ACTIVE' or is_active_status = 'Yes' then 
			is_active_ = true;		
		end if;
	
		  
		if entry_id = 0 then					
			
			insert into public.cl_tx_category  (
				org_id, category_name, is_active, c_meta, c_by, form_data
				)
				values (
					 org_id_, 					
					 category_name_,					
					 case 
					 	when is_active_status is null then
					 		true
					 	else						 	
				 			is_active_
					 end,				
					 row(ip_address_,user_agent_,now() at time zone 'utc'), 
					 usr_id_,
					 form_data_
				)
				returning db_id into ins_id;
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then		
				message = 'success';
				status = true;
				resp_data =  json_build_object('entry_id',ins_id);		
			end if;
		
		elsif entry_id > 0 then
		
			update public.cl_tx_category  as category
			   set u_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
		           u_by = usr_id_,
		           category_name  = category_name_,		         	                 
		           is_active = case 
			           			when is_active_status is not null then 
			           				is_active_
			           			else 
			           				is_active  
		           			  end,
       			   form_data = ((form_data)::jsonb || (form_data_)::jsonb)::json	        
			 where category.org_id = org_id_
		       and category.db_id = entry_id;
			
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then	
				message = 'success';
				status = true;
				resp_data =  json_build_object('entry_id',entry_id);
			end if;  
		end if;
		return jsonb_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
