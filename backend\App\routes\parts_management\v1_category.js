var express = require('express');
var router = express.Router();
const { getUserContextFrmReq } = require('../../api_models/utils/authrizor');
const model = require('../../api_models/parts_management/manage_category_model');
// const model = require("../api_models/ratings_model");
router.get('/overview_proto', function (req, res, next) {
    console.log('Got call for category overview proto');
    const model = setParamsToModel(req);
    model.getOverViewProto(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
router.get('/list', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getAllCategories(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
router.get('/proto/:entry_id', function (req, res, next) {
    const model = setParamsToModel(req);
    let entry_id = req.params.entry_id;
    model.getSingleEntry(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});
router.put('/:entry_id', function (req, res, next) {
    const model = setParamsToModel(req);
    let entry_id = req.params.entry_id ? req.params.entry_id : 0;
    model.createOrUpdateCategory(req.body, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

//pst api
router.post('/', function (req, res, next) {
    if (req.body.batch_data) {
        // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
        model.createOrUpdateCategoryBatch(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
        return;
    } else {
        model.createOrUpdateCategory(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
});
const setParamsToModel = (req) => {
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
