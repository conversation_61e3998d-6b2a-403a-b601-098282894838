import { DatePicker } from 'antd';
import moment from 'moment';
import {
    getPresetRangesForRangeDatePicker,
    getRatingFrFilter,
} from '../../../../util/helpers';
const filters = [
    {
        key: 'is_active_status',
        label: 'Status',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            {
                label: 'Active',
                value: true,
                icon: 'icon-check-cricle gx-text-green ',
            },
            {
                label: 'Inactive',
                value: false,
                icon: 'icon-check-circle-o gx-text-grey',
            },
        ],
    },
];

export { filters };
