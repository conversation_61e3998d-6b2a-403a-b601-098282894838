var sampleOperationResp = require('../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../utils/db_resp');
const users_model = require('../users_model');
const pagination_filters_utils = require('../utils/pagination_filters_utils');
const { json } = require('express');
class category_model {
    async getOverViewProto(query) {
        try {
            const { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            } else {
                return new sampleOperationResp(
                    true,
                    JSON.stringify({}),
                    HttpStatus.StatusCodes.OK
                );
            }
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getAllCategories(query) {
        try {
            const { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);

            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_all_category(
                form_data,
                page_no_,
                page_size_,
                filters_,
                search_query
            );

            if (!res || !res[0]) {
                return new sampleOperationResp(
                    false,
                    'Unknown error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const dbResp = new db_resp(res[0].tms_get_all_category);

            if (!dbResp.status) {
                return new sampleOperationResp(
                    false,
                    'Internal server Error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            } else {
                return new sampleOperationResp(
                    true,
                    JSON.stringify(dbResp.data),
                    HttpStatus.StatusCodes.OK
                );
            }
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getSingleEntry(query, entry_id = 0) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['entry_id'] = entry_id;

            const form_data = JSON.stringify(query);

            if (!this.db) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            console.log('Form db', form_data);

            const res = await this.db.tms_get_single_category(
                form_data,
                entry_id
            );

            if (!res || !res[0]) {
                return new sampleOperationResp(
                    false,
                    'Unknown error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const dbResp = new db_resp(res[0].tms_get_single_category);

            if (!dbResp.status) {
                return new sampleOperationResp(
                    false,
                    'Internal server Error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            } else {
                return new sampleOperationResp(
                    true,
                    JSON.stringify(dbResp.data),
                    HttpStatus.StatusCodes.OK
                );
            }
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async createOrUpdateCategory(query, entry_id = 0) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);

            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            console.log('form_data->', form_data);
            const res = await dbObj.tms_create_or_update_category(
                form_data,
                entry_id
            );

            if (!res || !res[0]) {
                return new sampleOperationResp(
                    false,
                    'Unknown error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const dbResp = new db_resp(res[0].tms_create_or_update_category);

            if (dbResp.code === 'sku_already_exists') {
                return new sampleOperationResp(
                    false,
                    dbResp.data,
                    HttpStatus.StatusCodes.CONFLICT
                );
            }

            if (!dbResp.status) {
                return new sampleOperationResp(
                    false,
                    'Internal server Error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            } else {
                return new sampleOperationResp(
                    true,
                    JSON.stringify(dbResp.data),
                    HttpStatus.StatusCodes.OK
                );
            }
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async createOrUpdateCategoryBatch(query) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['batch_data'] = query.batch_data;

            const form_data = JSON.stringify(query);

            if (!this.db) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            console.log('form_data=>>>>>', form_data);
            const res = await this.db.tms_create_category_batch(form_data);

            const dbResp = new db_resp(res[0].tms_create_category_batch);

            if (dbResp.code === 'sku_already_exists') {
                return new sampleOperationResp(
                    false,
                    dbResp.data,
                    HttpStatus.StatusCodes.CONFLICT
                );
            } else if (!dbResp.status) {
                return new sampleOperationResp(
                    false,
                    'Internal server Error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            } else {
                return new sampleOperationResp(
                    true,
                    JSON.stringify(dbResp.data),
                    HttpStatus.StatusCodes.OK
                );
            }
        } catch (error) {
            console.log('error', error);
            return new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.CONFLICT
            );
        }
    }

    async fatalDbError(error) {
        return new sampleOperationResp(
            false,
            error.message,
            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    set srvc_req_id(srvcReqId) {
        this.srvcReqId = srvcReqId;
    }

    get srvc_req_id() {
        return this.srvcReqId;
    }
    getFreshInstance(model) {
        const clonedInstance = new category_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new category_model();
