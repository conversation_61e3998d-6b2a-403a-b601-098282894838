/* Execution Master Product Details Styles */
.execution-master-container {
    padding: 20px;
}

.product-search-header {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
}

.product-headers {
    margin-bottom: 16px;
    font-weight: bold;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
}

.category-header {
    cursor: pointer;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
    transition: background-color 0.2s;
}

.category-header:hover {
    background-color: #f5f5f5;
    padding-left: 8px;
    border-radius: 4px;
}

.product-card {
    margin-bottom: 24px;
    padding: 20px;
    background-color: #fafafa;
    border-radius: 12px;
    border: 1px solid #e8e8e8;
    transition: box-shadow 0.2s, transform 0.1s;
}

.product-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.product-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 6px;
    color: #262626;
}

.product-sku {
    color: #8c8c8c;
    font-size: 12px;
    margin-bottom: 6px;
    font-weight: 500;
}

.product-description {
    color: #595959;
    font-size: 13px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.skills-column,
.manpower-column,
.duration-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.skill-input,
.manpower-input,
.duration-input {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.skill-input:focus,
.manpower-input:focus,
.duration-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-input {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
}

.category-filter {
    border-radius: 6px;
}

.products-count {
    font-weight: 600;
    color: #1890ff;
}

.service-type-tag {
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-card {
        padding: 16px;
    }
    
    .product-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }
    
    .product-name {
        font-size: 14px;
    }
    
    .skills-column,
    .manpower-column,
    .duration-column {
        gap: 8px;
    }
}

/* Animation for category expansion */
.category-content {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading states */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}
