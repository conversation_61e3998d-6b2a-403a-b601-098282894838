CREATE OR REPLACE FUNCTION public.tms_get_execution_master_by_vertical_brand(form_data_ json)
RETURNS json
LANGUAGE plpgsql AS
$$
DECLARE  
    status boolean;
    message text;
    resp_data json;
    vertical_id_ integer;
    brand_id_ integer;

BEGIN
    status = false;
    message = 'Internal_error';

    vertical_id_ = (form_data_->>'vertical_id')::int;
    brand_id_ = (form_data_->>'brand_id')::int;
    
    IF vertical_id_ IS NULL OR brand_id_ IS NULL THEN
        status = true;
        message = 'success';
        RETURN json_build_object('status', status, 'code', message, 'data', '{}');
    END IF;

    resp_data = array_to_json(array( 
        SELECT json_build_object(	
            'id', exec_master.id,
            'vertical_id', exec_master.vertical_id,
            'service_type_id', exec_master.service_type_id,
            'sku_id', exec_master.sku_id,
            'skill_1', exec_master.skill_1,
            'skill_2', exec_master.skill_2,
            'skill_3', exec_master.skill_3,
            'manpower_1', exec_master.manpower_1,
            'manpower_2', exec_master.manpower_2,
            'manpower_3', exec_master.manpower_3,
            'duration_1', exec_master.duration_1,
            'duration_2', exec_master.duration_2,
            'duration_3', exec_master.duration_3,
            'created_on', (exec_master.c_meta).time,
            'last_u_date', (exec_master.u_meta).time,
            'c_by', exec_master.c_by,
            'u_by', exec_master.u_by,
            'form_data', exec_master.form_data
        )	
        FROM execution_master AS exec_master	          
        WHERE exec_master.vertical_id = vertical_id_
          AND exec_master.service_type_id = brand_id_
        ORDER BY exec_master.sku_id
    ));

    IF json_array_length(resp_data) > 0 THEN 
        status = true;
        message = 'success';
    ELSE
        status = true;
        message = 'success';
        resp_data = '[]'::json;
    END IF;

    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);

END;
$$;
