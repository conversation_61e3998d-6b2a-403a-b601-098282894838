CREATE OR REPLACE FUNCTION public.tms_create_category_batch(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	
-- Declarations
declare
	-- Bare minimums
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;


	-- Form data
	batch_data_ json;
	_form_data_to_create_entry json;
	
	-- temp
  	_single_entry json;
  	ins_id uuid;
  	_form_data_proto json;
  	_single_entry_creation_resp json;
  	exception_hint text;

   
	-- Output
	ins_ids int[];
	_entry_ids_vs_query json default '{}';

begin
	status = false;
	message = 'Internal_error';

	org_id_ 		= form_data_->>'org_id';
	usr_id_ 		= form_data_->>'usr_id';
	ip_address_ 	= form_data_->>'ip_address';
	user_agent_ 	= form_data_->>'user_agent';
	
	-- Form data
	batch_data_ 	 = form_data_->'batch_data';
	_form_data_proto = form_data_::jsonb - 'batch_data';

	for _single_entry in select * from json_array_elements(batch_data_)
	loop
		_form_data_to_create_entry = _form_data_proto::jsonb || _single_entry::jsonb;
		raise notice '_single_entry %',_single_entry;
		_single_entry_creation_resp = tms_create_or_update_category(_form_data_to_create_entry);

		exception_hint = 'Failed row --> ' || (_single_entry->>'category_name') || ' code - ' || (_single_entry_creation_resp->'code') ;
     	perform pg_sleep(0.000001);-- wait 1 microsecond 		
--		raise exception 'Failed row --> %', _single_entry->>'{cust_full_name}'
--     	using HINT = exception_hint;
		
		if _single_entry_creation_resp->'status' then
--			raise notice 'entry_id %',(_single_entry_creation_resp->'data'->>'entry_id');
			-- entry creation successful
			ins_ids := ins_ids || (_single_entry_creation_resp->'data'->>'entry_id')::int;			
		 	_entry_ids_vs_query := _entry_ids_vs_query::jsonb || json_build_object((_single_entry_creation_resp->'data'->>'entry_id'), _form_data_to_create_entry)::jsonb;
		else
			raise exception 'Failed row --> %', _single_entry->>'category_name'
     			using HINT = exception_hint;
		end if;				
	
	end loop;
	
	status = true;
	message = 'success';
	resp_data =  json_build_object('entry_ids',ins_ids,'entry_ids_vs_query',_entry_ids_vs_query);

	return json_build_object('status',status,'code',message,'data',resp_data);
 
END;
$function$
;
