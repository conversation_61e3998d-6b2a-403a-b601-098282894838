CREATE OR REPLACE FUNCTION public.tms_get_view_data_execution(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		status boolean;
		message text;
		resp_data json default '{}'::json;
		pulse_tracker_fields json;
		vertical_list json;
		org_id_ int;
        vertical_id_ int;
        org_list json;
        vertical_skill_list json;
        srvc_type_id_ int;
        category_wise_product_list json;

	BEGIN
		status = false;
		message = 'Internal_error';
		
		org_id_ = form_data->>'org_id';
	    vertical_id_ = form_data->>'vertical_id';
        srvc_type_id_ = form_data->>'brand_id';
	
		vertical_list = tms_hlpr_get_verticals_list(org_id_);
	    if vertical_id_ > 0 then
			org_list = array_to_json(array(
                select jsonb_build_object(
                        'value',service_types.service_type_id,
                        'label',orgs.nickname || ' - ' || service_types.title
                       
                    )
                from cl_tx_orgs_settings as org_settings
                inner join cl_cf_service_types as service_types 
                    on service_types.service_type_id = any(
                                    array(
                                        select json_array_elements_text(org_settings.settings_data->'srvc_type_id')
                                    )::int[]
                                )
                inner join cl_tx_orgs as orgs 
                    on orgs.org_id = service_types.org_id 
                where org_settings.db_id = vertical_id_
                group by orgs.org_id, service_types.service_type_id 	
                order by orgs.nickname asc
		));	
            if json_array_length(org_list) > 0 then
	   	    resp_data = jsonb_set(resp_data::jsonb,'{org_list}'::text[],org_list::jsonb,true);
            end if;

            vertical_skill_list = array_to_json(array(
                                select jsonb_build_object(
                                        'value',skill.db_id,
                                        'label',skill.skill_name
                                    )
                                from cl_tx_skills as skill
                                inner join cl_tx_skill_map as skill_map
                                    on skill_map.skill_id = skill.db_id
                                where skill_map.vertical_id = vertical_id_
                                group by skill.db_id
                                order by skill.skill_name asc
            ));
            if json_array_length(vertical_skill_list) > 0 then
                resp_data = jsonb_set(resp_data::jsonb,'{vertical_skill_list}'::text[],vertical_skill_list::jsonb,true);
            end if;

            --categorywise prodyuct list
           
        end if;
		-- now need to get vertical skill list , category with product list
        if srvc_type_id_ > 0 then
            --get product detials with category from produnct table 
            -- first get catrogry inside this get product list
            category_wise_product_list =  (SELECT json_agg(category_data) AS categories
                                            FROM (
                                                SELECT 
                                                    cat.db_id AS category_id,
                                                    cat.category_name,
                                                    json_agg(
                                                        jsonb_build_object(
                                                            'product_id', sku.db_id,
                                                            'sku_code', sku.sku,
                                                            'product_name', sku.product_name,
                                                            'description', sku.description
                                                        )
                                                    ) AS product_details
                                                FROM cl_tx_category cat
                                                JOIN cl_tx_product_sku sku 
                                                  ON cat.db_id = sku.category
                                                WHERE srvc_type_id_ = ANY(sku.service_type)   -- 👈 filter by service type array
                                                GROUP BY cat.db_id, cat.category_name
                                                ORDER BY cat.category_name
                                            ) AS category_data);
            --get category list
            if json_array_length(category_wise_product_list) > 0 then
                resp_data = jsonb_set(resp_data::jsonb,'{category_wise_product_list}'::text[],category_wise_product_list::jsonb,true);
            end if;
        end if;

        
	
		status = true;
		message = 'success';
		
		resp_data = jsonb_set(resp_data::jsonb,'{vertical_list}'::text[],vertical_list::jsonb,true);
		return json_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
