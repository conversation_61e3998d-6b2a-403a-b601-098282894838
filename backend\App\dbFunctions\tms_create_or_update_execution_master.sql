CREATE OR REPLACE FUNCTION tms_create_or_update_execution_master(form_data_ json)
RETURNS json
LANGUAGE plpgsql AS
$$
DECLARE
    status boolean;
    message text;
    resp_data json;
    affected_rows integer;
    ins_id integer;

    -- Extract metadata from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;

    -- Extract main form fields
    v_vertical_id INT;
    v_service_type_id INT;
    v_execution_mode TEXT;

    category JSONB;
    product JSONB;
    product_id TEXT;

BEGIN
    status = false;
    message = 'Internal_error';

    -- Extract metadata
    org_id_ = (form_data_->>'org_id')::int;
    usr_id_ = (form_data_->>'usr_id')::uuid;
    ip_address_ = form_data_->>'ip_address';
    user_agent_ = form_data_->>'user_agent';

    -- Extract main form fields
    v_vertical_id = (form_data_->>'vertical_list')::INT;
    v_service_type_id = (form_data_->>'brand')::INT;
    v_execution_mode = form_data_->>'execution_mode';

    -- loop over categories
    FOR category IN SELECT jsonb_each(form_data_->'product_wise') LOOP
        -- loop over products inside each category
        FOR product IN SELECT jsonb_each(category.value) LOOP
            product_id := product.key;

            INSERT INTO execution_master (
                org_id,
                vertical_id,
                service_type_id,
                sku_id,
                skill_1, skill_2, skill_3,
                manpower_1, manpower_2, manpower_3,
                duration_1, duration_2, duration_3,
                c_meta, c_by, form_data
            )
            VALUES (
                org_id_,
                v_vertical_id,
                v_service_type_id,
                product_id::INT,
                (product.value->>'skill_1')::INT,
                (product.value->>'skill_2')::INT,
                (product.value->>'skill_3')::INT,
                (product.value->>'manpower_1'),
                (product.value->>'manpower_2'),
                (product.value->>'manpower_3'),
                (product.value->>'duration_1'),
                (product.value->>'duration_2'),
                (product.value->>'duration_3'),
                row(ip_address_, user_agent_, now() at time zone 'utc'),
                usr_id_,
                form_data_
            )
            ON CONFLICT (sku_id) DO UPDATE
            SET
                skill_1 = EXCLUDED.skill_1,
                skill_2 = EXCLUDED.skill_2,
                skill_3 = EXCLUDED.skill_3,
                manpower_1 = EXCLUDED.manpower_1,
                manpower_2 = EXCLUDED.manpower_2,
                manpower_3 = EXCLUDED.manpower_3,
                duration_1 = EXCLUDED.duration_1,
                duration_2 = EXCLUDED.duration_2,
                duration_3 = EXCLUDED.duration_3,
                vertical_id = EXCLUDED.vertical_id,
                service_type_id = EXCLUDED.service_type_id,
                u_meta = row(ip_address_, user_agent_, now() at time zone 'utc'),
                u_by = usr_id_,
                form_data = EXCLUDED.form_data;
        END LOOP;
    END LOOP;

    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    IF affected_rows > 0 THEN
        status = true;
        message = 'success';
        resp_data = json_build_object('affected_rows', affected_rows);
    END IF;

    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$$;
