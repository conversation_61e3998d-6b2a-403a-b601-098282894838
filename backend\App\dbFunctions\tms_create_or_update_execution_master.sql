CREATE OR REPLACE FUNCTION tms_create_or_update_execution_master(form_data_ json)
RETURNS VOID
LANGUAGE plpgsql AS
$$
DECLARE
    v_vertical_id INT := (p_data->>'vertical_list')::INT;
    v_service_type_id INT := (p_data->>'brand')::INT;
    v_execution_mode TEXT := p_data->>'execution_mode';

    category JSONB;
    product JSONB;
    product_id TEXT;
BEGIN
    -- loop over categories
    FOR category IN SELECT jsonb_each(p_data->'product_wise') LOOP
        -- loop over products inside each category
        FOR product IN SELECT jsonb_each(category.value) LOOP
            product_id := product.key;

            INSERT INTO execution_master (
                vertical_id,
                service_type_id,
                execution_mode,
                product_id,
                skill_1, skill_2, skill_3,
                manpower_1, manpower_2, manpower_3,
                duration_1, duration_2, duration_3,
                form_data  -- 👈 raw JSON payload
            )
            VALUES (
                v_vertical_id,
                v_service_type_id,
                v_execution_mode,
                product_id::INT,
                (product.value->>'skill_1')::INT,
                (product.value->>'skill_2')::INT,
                (product.value->>'skill_3')::INT,
                (product.value->>'manpower_1'),
                (product.value->>'manpower_2'),
                (product.value->>'manpower_3'),
                (product.value->>'duration_1'),
                (product.value->>'duration_2'),
                (product.value->>'duration_3'),
                p_data  -- 👈 save the entire input JSON
            )
            ON CONFLICT (product_id) DO UPDATE
            SET
                skill_1 = EXCLUDED.skill_1,
                skill_2 = EXCLUDED.skill_2,
                skill_3 = EXCLUDED.skill_3,
                manpower_1 = EXCLUDED.manpower_1,
                manpower_2 = EXCLUDED.manpower_2,
                manpower_3 = EXCLUDED.manpower_3,
                duration_1 = EXCLUDED.duration_1,
                duration_2 = EXCLUDED.duration_2,
                duration_3 = EXCLUDED.duration_3,
                vertical_id = EXCLUDED.vertical_id,
                service_type_id = EXCLUDED.service_type_id,
                execution_mode = EXCLUDED.execution_mode,
                form_data = EXCLUDED.form_data;
        END LOOP;
    END LOOP;
END;
$$;
