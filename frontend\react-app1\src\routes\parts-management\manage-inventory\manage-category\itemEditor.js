import { <PERSON><PERSON>, <PERSON><PERSON>, Col, <PERSON>lapse, Form, Modal, Row } from 'antd';
import FormBuilder from 'antd-form-builder';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import { UploadOutlined } from '@ant-design/icons';
import BulkUploader from '../../../../components/wify-utils/BulkUploader';
import {
    convertDateFieldsToMoments,
    convertUTCToDisplayTime,
    hasAnyFileChanged,
} from '../../../../util/helpers';
import UserName from '../../../../components/wify-utils/UserName';

const protoUrl = '/parts_management/category/proto';
const submitUrl = '/parts_management/category';

const ItemEditor = ({
    itemEditorData,
    showEditor,
    onClose,
    editMode,
    onChange,
    readOnly,
}) => {
    const [form] = Form.useForm();
    const initialValues = itemEditorData || {};
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState();
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const initViewData = () => {
        if (viewData === undefined) {
            setIsLoadingViewData(true);
            const url =
                protoUrl + '/' + (editMode ? itemEditorData?.category_id : 0);

            const onComplete = (resp) => {
                setViewData(resp.data);
                setIsLoadingViewData(false);
            };
            const onError = (error) => {
                setError(http_utils.decodeErrorToMessage(error));
                setIsLoadingViewData(false);
            };
            http_utils.performGetCall(url, {}, onComplete, onError);
        }
    };

    useEffect(() => {
        initViewData();
    }, [initViewData]);

    const meta = (frBulk = false) => {
        return {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'category_details',
                    colSpan: 4,
                    render: () => (
                        <div className="gx-mt-2">
                            <b>Category Details</b>
                            <hr />
                        </div>
                    ),
                },
                {
                    key: 'category_name',
                    label: 'Category name',
                    colSpan: 2,
                    placeholder: 'Eg: Bearing',
                    required: true,
                    // disabled: editMode,
                },
                //above is active only for bulk false
                !frBulk && {
                    key: 'is_active',
                    label: 'Active',
                    colSpan: 4,
                    widget: 'switch',
                    initialValue: viewData?.is_active || true,
                },
            ],
        };
    };

    const getInitialValues = () => {
        let prefillFormData = { ...viewData?.form_data, ...viewData };
        if (prefillFormData) {
            prefillFormData = convertDateFieldsToMoments(
                prefillFormData,
                meta().fields
            );
        }
        return prefillFormData;
    };

    const getDescriptionFrAlert = () => {
        return (
            <p>
                {viewData?.is_active ? (
                    <>
                        {viewData?.last_u_date ? (
                            <>
                                Last Updated -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.last_u_date
                                    )}
                                </b>
                                , by {getUserNameWhoActivateandDeactivatesYou()}
                                {<br></br>}
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        ) : (
                            <>
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        )}
                    </>
                ) : (
                    <>
                        {viewData?.last_u_date ? (
                            <>
                                Inactive from -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.last_u_date
                                    )}
                                </b>
                                , by {getUserNameWhoActivateandDeactivatesYou()}
                                {<br></br>}
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        ) : (
                            <>
                                Created on -{' '}
                                <b>
                                    {convertUTCToDisplayTime(
                                        viewData?.created_on
                                    )}
                                </b>
                                {getUserNameWhoCreatedYou() != null
                                    ? getUserNameWhoCreatedYou()
                                    : ''}
                            </>
                        )}
                    </>
                )}
            </p>
        );
    };

    const getUserNameWhoActivateandDeactivatesYou = () => {
        return <UserName id={viewData?.is_active_state_change_by} />;
    };

    const getUserNameWhoCreatedYou = () => {
        return <UserName id={viewData?.c_by} prefix={', by'} />;
    };

    /** ========== Submit Form ========== */
    const submitForm = (data) => {
        setIsFormSubmitting(true);
        let params = data;

        const onComplete = () => {
            setIsFormSubmitting(false);
            onClose();
            onChange();
        };
        const onError = (error) => {
            setError(http_utils.decodeErrorToMessage(error));
            setIsFormSubmitting(false);
        };

        if (editMode) {
            http_utils.performPutCall(
                submitUrl + '/' + itemEditorData?.category_id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(submitUrl, params, onComplete, onError);
        }
    };
    /** ========== Render ========== */
    // if (!showEditor) return null;

    // if (viewData === undefined) {
    //     return (
    //         <div className="gx-loader-view gx-loader-position">
    //             <CircularProgress />
    //         </div>
    //     );
    // }

    return (
        <Modal
            title={editMode ? 'Edit' : 'Add Category'}
            open={showEditor}
            visible={showEditor}
            confirmLoading={isFormSubmitting}
            onCancel={onClose}
            width={800}
            style={{ marginTop: '-70px' }}
            bodyStyle={{
                minHeight: '85vh',
                padding: '18px',
                paddingTop: '0px',
            }}
            footer={false}
        >
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData == undefined && editMode ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <Row>
                    {!editMode && (
                        <Col xs={24} className="gx-my-1">
                            <Collapse>
                                <Collapse.Panel
                                    header={
                                        <span className="gx-text-primary">
                                            <UploadOutlined className="gx-mr-2" />
                                            Click here for Bulk creation
                                        </span>
                                    }
                                >
                                    <BulkUploader
                                        submitUrl={submitUrl}
                                        dataProto={meta(true).fields}
                                        onDataModified={() => onChange()}
                                    />
                                </Collapse.Panel>
                            </Collapse>
                        </Col>
                    )}
                    <Col xs={24}>
                        <Form
                            className="gx-w-100"
                            layout="vertical"
                            initialValues={getInitialValues()}
                            form={form}
                            onFinish={submitForm}
                        >
                            <FormBuilder
                                form={form}
                                meta={meta()}
                                disabled={readOnly}
                            />
                            {/* micSections, cameraSections, fileSections rendering same as before */}
                            {error && <p className="gx-text-red">{error}</p>}
                            {editMode && (
                                <Alert
                                    description={getDescriptionFrAlert()}
                                    type={
                                        viewData?.is_active
                                            ? 'success'
                                            : 'error'
                                    }
                                />
                            )}
                            <Button
                                type="primary"
                                htmlType="submit"
                                disabled={isFormSubmitting || readOnly}
                            >
                                {editMode ? 'Save' : 'Add Category'}
                            </Button>
                        </Form>
                    </Col>
                </Row>
            )}
        </Modal>
    );
};

export default ItemEditor;
