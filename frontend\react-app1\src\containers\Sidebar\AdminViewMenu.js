import React from 'react';
import { Menu, Tag, Tooltip } from 'antd';
import { <PERSON> } from 'react-router-dom';
import { FaUserAstronaut } from 'react-icons/fa';
import { LuUsers2 } from 'react-icons/lu';
import { TbTransform, TbTransferIn } from 'react-icons/tb';
import { CodeOutlined, MobileOutlined } from '@ant-design/icons';
import { FaBusinessTime } from 'react-icons/fa';

import CaptureLoginAttendance from '../../components/WIFY/CaptureLoginAttendance';
import ConfigHelpers from '../../util/ConfigHelpers';
import IntlMessages from '../../util/IntlMessages';
import { useSelector } from 'react-redux';
import {
    NAV_STYLE_NO_HEADER_EXPANDED_SIDEBAR,
    NAV_STYLE_NO_HEADER_MINI_SIDEBAR,
    THEME_TYPE_LITE,
} from '../../constants/ThemeSetting';
import { getLinktoObject } from '../../util/helpers';
import { MdOutlineInventory } from 'react-icons/md';

const SubMenu = Menu.SubMenu;
const MenuItemGroup = Menu.ItemGroup;

const AdminViewMenu = () => {
    let { navStyle, themeType } = useSelector(({ settings }) => settings);
    let { pathname } = useSelector(({ common }) => common);

    const { access_service_routes, config_data, roles, access_static_routes } =
        useSelector(({ auth }) => auth.authUser);

    const getNavStyleSubMenuClass = (navStyle) => {
        if (navStyle === NAV_STYLE_NO_HEADER_MINI_SIDEBAR) {
            return 'gx-no-header-submenu-popup';
        }
        return '';
    };
    const selectedKeys = pathname.substr(1);
    const defaultOpenKeys = selectedKeys.split('/')[1];
    return (
        <Menu
            defaultOpenKeys={[defaultOpenKeys]}
            selectedKeys={[selectedKeys]}
            theme={themeType === THEME_TYPE_LITE ? 'lite' : 'dark'}
            mode="inline"
            className="gx-mb-5"
        >
            <MenuItemGroup
                key="main"
                className="gx-menu-group"
                title={<IntlMessages id="sidebar.main" />}
            ></MenuItemGroup>

            {/* <Menu.Item key="sample">
              <Link to="/sample"><i className="icon icon-widgets"/>
                <span><IntlMessages id="sidebar.samplePage"/></span>
              </Link>
            </Menu.Item> */}
            <Menu.Item key="dashboard">
                <Link to="/dashboard">
                    <i className="icon icon-dasbhoard" />
                    <span>Dashboard Admin</span>
                </Link>
            </Menu.Item>

            <Menu.Item key="users">
                <Link to="/users">
                    <i className="icon icon-avatar" />
                    <span>Users</span>
                </Link>
            </Menu.Item>
            {!ConfigHelpers.isServiceProvider() && (
                <>
                    <Menu.Item key="customer">
                        <Link to="/customer">
                            <i className="icon icon-avatar" />
                            <span>Customer</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="srvcProviders">
                        <Link to="/srvcProviders">
                            <i className="icon icon-social" />
                            <span>Service Providers</span>
                        </Link>
                    </Menu.Item>
                </>
            )}
            {process.env.NODE_ENV != 'production' ? (
                <Menu.Item key="devlPlayground">
                    <Link to="/devlPlayground">
                        <i className="icon icon-chart-tree" />
                        <span>Devl Playground</span>
                    </Link>
                </Menu.Item>
            ) : null}

            <MenuItemGroup key="setup" className="gx-menu-group" title="Setup">
                {/* <Menu.Item key="setup/custom-fields">
                <Link to="/setup/custom-fields"><i className="icon icon-ckeditor"/>
                  <span>Custom fields</span>
                </Link>
              </Menu.Item>    */}

                <SubMenu
                    key="user-config"
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon icon-auth-screen" />
                            <span>User configuration</span>
                        </span>
                    }
                >
                    <Menu.Item key="setup/user-config/roles">
                        <Link to="/setup/user-config/roles">
                            <i className="icon icon-profile" />
                            <span>Roles</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="setup/user-config/locations">
                        <Link to="/setup/user-config/locations">
                            <i className="icon icon-crm" />
                            <span>Locations</span>
                        </Link>
                    </Menu.Item>

                    {!ConfigHelpers.isServiceProvider() && (
                        <Menu.Item key="setup/user-config/zones">
                            <Link to="/setup/user-config/zones">
                                <i className="icon wy-sidebar-child-icon wy-icon-zone"></i>
                                <span>Zones</span>
                            </Link>
                        </Menu.Item>
                    )}

                    <Menu.Item key="setup/user-config/fields">
                        <Link to="/setup/user-config/fields">
                            <i className="icon icon-widgets" />
                            <span>User Fields</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="setup/user-config/restrictions">
                        <Link to="/setup/user-config/restrictions">
                            <i className="icon icon-error" />
                            <span>Restrictions</span>
                        </Link>
                    </Menu.Item>

                    {/* <Menu.Item key="setup/user-config/Fields">
                        <Link to="/setup/user-config/Fields"><i className="icon icon-wysiwyg"/>
                          <span>Fields</span>
                        </Link>
                      </Menu.Item> */}
                </SubMenu>

                <SubMenu
                    key="srvc-req"
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon icon-extra-components" />
                            <span>Service configuration</span>
                        </span>
                    }
                >
                    <Menu.Item key="setup/srvc-req/types">
                        <Link to="/setup/srvc-req/types">
                            <i className="icon icon-timeline-with-icons" />
                            <span>Service types</span>
                        </Link>
                    </Menu.Item>
                    <Menu.Item key="setup/srvc-req/sub-tasks-types">
                        <Link to="/setup/srvc-req/sub-tasks-types">
                            <i className="icon icon-basic-calendar" />
                            <span>Subtask types</span>
                        </Link>
                    </Menu.Item>
                    {
                        /*ConfigHelpers.isServiceProvider() && */
                        <Menu.Item key="setup/srvc-req/status-group">
                            <Link to="/setup/srvc-req/status-group">
                                <i className="icon icon-crm" />
                                <span>Status groups</span>
                            </Link>
                        </Menu.Item>
                    }
                    {ConfigHelpers.isServiceProvider() && (
                        <Menu.Item key="setup/srvc-req/service-provider-fields">
                            <Link to="/setup/srvc-req/service-provider-fields">
                                <i className="icon icon-widgets" />
                                <span>Verticals</span>
                            </Link>
                        </Menu.Item>
                    )}

                    {/* {
                  ConfigHelpers.isServiceProvider() && 
                  <Menu.Item key="setup/srvc-req/service-provider-authorities">
                    <Link to="/setup/srvc-req/service-provider-authorities">
                    <i className="icon icon-ckeditor"/>
                      <span>SP Authorities</span>
                    </Link>
                  </Menu.Item>
                } */}

                    {/* <Menu.Item key="setup/srvc-req/alerts">
                  <Link to="/setup/srvc-req/alerts"><i className="icon icon-alert"/>
                    <span>Notications</span>
                  </Link>
                </Menu.Item> */}
                </SubMenu>

                {ConfigHelpers.isServiceProvider() && (
                    <SubMenu
                        key="execution"
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <span>
                                {' '}
                                <i className="icon icon-cog" />
                                <span>Execution</span>
                            </span>
                        }
                    >
                        <Menu.Item key="setup/execution/execution-master">
                            <Link to="/setup/execution/execution-master">
                                <i className="icon icon-setting" />
                                <span>Execution Master</span>
                            </Link>
                        </Menu.Item>
                    </SubMenu>
                )}

                <SubMenu
                    key="automation-deployment"
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon icon-crm" />
                            <span>Automate Deployment</span>
                        </span>
                    }
                >
                    <Menu.Item key="setup/automation-deployment/pool-view">
                        <Link to="/setup/automation-deployment/pool-view">
                            <i className="icon icon-timeline-left-align" />
                            <span>Pool View</span>
                        </Link>
                    </Menu.Item>
                    <Menu.Item key="setup/automation-deployment/pulse-tracker">
                        <Link to="/setup/automation-deployment/pulse-tracker">
                            <i className="icon icon-timeline-left-align" />
                            <span>Pulse Tracker</span>
                        </Link>
                    </Menu.Item>
                    {ConfigHelpers.isServiceProvider() && (
                        <Menu.Item key="setup/automation-deployment/lambda-based">
                            <Link to="/setup/automation-deployment/lambda-based">
                                <i className="icon icon-ckeditor" />
                                <span>Lambda based</span>
                            </Link>
                        </Menu.Item>
                    )}
                    {ConfigHelpers.isServiceProvider() && (
                        <Menu.Item key="setup/automation-deployment/job-broadcast">
                            <Link to="/setup/automation-deployment/job-broadcast">
                                <i className="icon icon-alert" />
                                <span>JobBroadcasts</span>
                            </Link>
                        </Menu.Item>
                    )}
                </SubMenu>
            </MenuItemGroup>

            <SubMenu
                key="rate-config"
                popupClassName={getNavStyleSubMenuClass(navStyle)}
                title={
                    <span>
                        <i className="icon icon-extra-components" />
                        <span>Rate Config</span>
                    </span>
                }
            >
                <Menu.Item key="setup/rate-config/billing-fields">
                    <Link to="/setup/rate-config/billing-fields">
                        <i className="icon icon-cascader" />
                        <span>Billing Fields</span>
                    </Link>
                </Menu.Item>
                {ConfigHelpers.isServiceProvider() && (
                    <Menu.Item key="setup/rate-config/rate-card">
                        <Link to="/setup/rate-config/rate-card">
                            <i className="icon icon-all-contacts" />
                            <span>Rate Card</span>
                        </Link>
                    </Menu.Item>
                )}
            </SubMenu>

            {ConfigHelpers.isServiceProvider() && (
                <SubMenu
                    key="capacity"
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            <i className="icon icon-chart-scatter" />
                            <span>Capacity</span>
                        </span>
                    }
                >
                    <Menu.Item key="setup/capacity/service-hubs">
                        <Link to="/setup/capacity/service-hubs">
                            <i className="icon icon-cascader" />
                            <span>Service Hubs</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="setup/capacity/availability-slots">
                        <Link to="/setup/capacity/availability-slots">
                            <FaBusinessTime className="icon" />
                            <span>Availability timeslots</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="setup/capacity/time-slots">
                        <Link to="/setup/capacity/time-slots">
                            <FaBusinessTime className="icon" />
                            <span>Time Slots</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key="setup/capacity/vertical-skills">
                        <Link to="/setup/capacity/vertical-skills">
                            <i className="icon icon-chart-radial" />
                            <span>Vert. Skills</span>
                        </Link>
                    </Menu.Item>
                    <Menu.Item key="setup/capacity/capacity-settings">
                        <Link to="/setup/capacity/capacity-settings">
                            <i className="icon icon-components" />
                            <span>Cap. settings</span>
                        </Link>
                    </Menu.Item>
                </SubMenu>
            )}

            {!ConfigHelpers.isServiceProvider() && (
                <SubMenu
                    key="inventory-config"
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            <i className="icon icon-wysiwyg" />
                            <span>Inventory Config</span>
                        </span>
                    }
                >
                    <Menu.Item key="setup/inventory-config/manage-custom-fields">
                        <Link to="/setup/inventory-config/manage-custom-fields">
                            <MdOutlineInventory className="icon" />
                            <span>Custom Fields</span>
                        </Link>
                    </Menu.Item>
                </SubMenu>
            )}

            <Menu.Item key="rating/rating-templates">
                <Link to="/rating/rating-templates">
                    <i className="icon icon-star-half" />
                    <span>Rating Templates</span>
                </Link>
            </Menu.Item>

            <Menu.Item key="setup/srvc-req/settings">
                <Link to="/setup/srvc-req/settings">
                    <i className="icon icon-setting" />
                    <span>Settings</span>
                </Link>
            </Menu.Item>

            <Menu.Item key="api-docs">
                <Link to="/api-docs">
                    {/* <i className="icon icon-transfer"/> */}
                    <CodeOutlined
                        style={{ fontSize: '20px' }}
                        className="gx-mr-2"
                    />
                    <span className="gx-mr-1 gx-vertical-align-middle">
                        API
                    </span>
                </Link>
            </Menu.Item>

            <Menu.Item key="toggle-role">
                <Link to="/toggle-role">
                    <i className="icon icon-transfer" />
                    <span>User view</span>
                </Link>
            </Menu.Item>
        </Menu>
    );
};

export default AdminViewMenu;
