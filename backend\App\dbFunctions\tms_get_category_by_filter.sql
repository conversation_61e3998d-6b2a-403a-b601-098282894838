CREATE OR REPLACE FUNCTION public.tms_get_category_by_filter(requester_info json, page_no integer, page_size integer, filter_ json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	-- Declarations
	declare
	-- 	Bare minimums
		status boolean;
		message text;

		data_ json;
		org_id_ integer;
	    usr_id_ uuid;
	
	--  Temps
	
		filter_search_query text;
	    filter_is_active_status text[];
	    filter_has_serial_no_ text[];

	   	isSrvcPrvdr boolean default false;
		
	begin
		status = false;
		message = 'Internal_error';
		org_id_ = json_extract_path_text(requester_info,'org_id')::int;
	    usr_id_ = json_extract_path_text(requester_info,'usr_id');
	
	    filter_is_active_status = array( select json_array_elements_text(json_extract_path(filter_,'is_active_status')));
	    filter_has_serial_no_ = array( select json_array_elements_text(json_extract_path(filter_,'has_serial_no')));
	   	   
	   	isSrvcPrvdr = tms_hlpr_is_org_srvc_prvdr(org_id_);

		-- Search
		if search_query !='' then
			filter_search_query = concat('%',search_query,'%');								    			
		end if;	
		
		data_ = array_to_json(array(
				select 						
						jsonb_build_object(			
							'id',category.db_id,
							'category_name',category.category_name ,
							'full_count', count( category.db_id) OVER() 
						)							
				   from cl_tx_category  as category				 				
				  where category.org_id = org_id_	
				    and (
				        	(
				  				filter_is_active_status is null
				  				or
				  				cardinality(filter_is_active_status) = 0
				  				or
				  				'-1' = any(filter_is_active_status)
				  			)
				  			or
				  			(
				  				cardinality(filter_is_active_status) > 0
				  				and 
				  				(category.is_active)::text = any(filter_is_active_status)						  				
				  			)
					    )
				    and (
				  			(
								search_query = ''			  			
				  			)						  			
				  			or
				  			(
				  				category.category_name ilike filter_search_query							   			
				  			)			   	  		
					    )	
				  group by category.db_id, category.category_name 
				  order by category.category_name
				  limit page_size
				 offset ( (page_no - 1) * page_size ) 				
				));
		
		raise notice 'data_ %',data_;	
		return data_;
	
	end ;
$function$
;
