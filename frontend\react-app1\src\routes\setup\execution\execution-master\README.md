# Execution Master - Product Skills Management

This component provides a UI for managing product categories, SKUs, and their associated skills, manpower, and duration requirements when a brand is selected.

## Features

### 1. **Brand Selection**
- Select a vertical first, then choose a brand/service
- Product details appear only after brand selection

### 2. **Product Management Interface**
- **Search**: Search products by name or SKU
- **Filter**: Filter by category (All Categories, Assembly & Installation, Smart Solutions)
- **Product Count**: Shows total number of products

### 3. **Product Details Display**
- **Product Information**: Name, SKU, description, and service type
- **Skills**: Editable dropdown selects for each skill (skill_1, skill_2, skill_3)
- **Manpower**: Text input fields for manpower requirements
- **Duration**: Text input fields for duration estimates

### 4. **Data Structure**
The component manages skills data in the following format:
```javascript
{
  "product_id": {
    "skill_1": "basic_assembly",
    "skill_2": "electrical_work", 
    "skill_3": "carpentry",
    "manpower_1": "2",
    "manpower_2": "1",
    "manpower_3": "1",
    "duration_1": "120",
    "duration_2": "60", 
    "duration_3": "90"
  }
}
```

### 5. **Available Skills Options**
- Basic Assembly
- Advanced Assembly  
- Electrical Work
- Plumbing
- Carpentry
- Smart Device Setup

### 6. **Interactive Features**
- **Expandable Categories**: Click category headers to expand/collapse
- **Real-time Updates**: All changes are logged to console
- **Form Integration**: Skills data is included in form submission

## Usage

1. **Select Vertical**: Choose from available verticals
2. **Select Brand**: Choose a brand/service (triggers product display)
3. **Search/Filter**: Use search bar or category filter to find specific products
4. **Configure Skills**: 
   - Select appropriate skills from dropdowns
   - Enter manpower requirements (numbers)
   - Enter duration estimates (in minutes)
5. **Submit**: All data including skills configuration is submitted

## Static Data (Demo)

Currently uses static data with two categories:
- **Assembly & Installation** (2 products)
  - Furniture Assembly Kit (FAK-001)
  - Premium Assembly Kit (PAK-002)
- **Smart Solutions** (2 products)  
  - Smart Home Setup (SHS-003)
  - IoT Device Configuration (IOT-004)

## Styling

Custom CSS classes provide:
- Responsive design
- Hover effects
- Smooth animations
- Professional appearance
- Mobile-friendly layout

## Console Logging

The component logs skills data updates to console for debugging and development purposes.
