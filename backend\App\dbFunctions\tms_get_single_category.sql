CREATE OR REPLACE FUNCTION public.tms_get_single_category(form_data_ json, entry_id_ bigint)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	--Declaration
declare  
	status boolean;
	message text;
	resp_data json;
	org_id_ integer;

begin
		
	status = false;
	message = 'Internal_error';

	org_id_ = (form_data_->>'org_id')::int;

    if entry_id_ = 0 then 
    	status = true;
		message = 'success';
		return jsonb_build_object('status',status,'code',message,'data','{}');    	
    end if;

	resp_data = array_to_json(array( 
			select jsonb_build_object(	
						'category_id',category.db_id,				
						'category_name',category.category_name ,						
						'status',case 
									when category.is_active then
										'Active'
									else 
										'Inactive'
									end,
						'is_active',category.is_active,
						'created_on',(category.c_meta).time,
						'last_u_date',(category.u_meta).time,
						'is_active_state_change_by',case 
														when (category.u_by is null) then 
															category.c_by  
														else 
															category.u_by  
														end,
						'c_by', category.c_by  ,
						'form_data',category.form_data 
					)	
		      from cl_tx_category  as category	          
			 where category.org_id = org_id_
			   and category.db_id = entry_id_
			));
	
	if json_array_length(resp_data) > 0 then 
		status = true;
		message = 'success';
	end if;

	return jsonb_build_object('status',status,'code',message,'data',resp_data->0);

	END;
$function$
;
