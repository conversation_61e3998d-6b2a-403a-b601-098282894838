CREATE OR REPLACE FUNCTION public.tms_get_all_category(requester_info json, page_no integer, page_size integer, filter_ json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	--Declaration
	declare
	-- 	Bare minimums
		status boolean;
		message text;
	
		resp_data json;
		data_ json;
		org_id_ integer;
	    usr_id_ uuid;

		matching_ids_json json;
		matching_ids bigint[];
		temp_id bigint;
	
		pagination json;
		total integer;
	    filter_search_query text;
		
	begin
		status = false;
		message = 'Internal_error';
		org_id_ = json_extract_path_text(requester_info,'org_id')::int;
	    usr_id_ = json_extract_path_text(requester_info,'usr_id');
	
		matching_ids_json = (select tms_get_category_by_filter(requester_info, page_no, page_size, filter_, search_query));
		raise notice 'matching_ids_json %',matching_ids_json;
		total = matching_ids_json->0->>'full_count';			
	   
		FOR single_id_index IN 0..json_array_length(matching_ids_json) - 1 loop		
		  temp_id = json_extract_path_text(matching_ids_json -> single_id_index,'id');
	 	  raise notice 'temp_id %',temp_id;
	--	  total = json_extract_path_text(matching_ids_json -> single_id_index,'full_count');
		 	
	      matching_ids = array_append(matching_ids, temp_id);
	    END LOOP;
		raise notice 'matching_ids %',matching_ids;
				
		data_ = array_to_json(array( 
			select jsonb_build_object(	
						'category_id',category.db_id,
						'category_name',category.category_name ,						
						'status',case 
									when category.is_active then
										'Active'
									else 
										'Inactive'
									end,
						'is_active',category.is_active
					)	
		      from cl_tx_category  as category	
			 where category.org_id = org_id_
			   and category.db_id = any(matching_ids)
			 order by category.category_name 
			
			));
	--	total = json_extract_path_text(data_->0,'total')::integer;
		pagination = jsonb_build_object('total', total  ,'current', page_no);
	    resp_data =  jsonb_build_object('pagination',pagination,'data',data_);
	   
		status = true;
	    message = 'success';
	   
		return json_build_object('status',status,'code',message,'data',resp_data);
	end ;
$function$
;
