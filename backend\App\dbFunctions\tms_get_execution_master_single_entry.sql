CREATE OR REPLACE FUNCTION public.tms_get_execution_master_single_entry(form_data_ json, entry_id_ bigint)
RETURNS json
LANGUAGE plpgsql AS
$$
DECLARE  
    status boolean;
    message text;
    resp_data json;
    org_id_ integer;

BEGIN
    status = false;
    message = 'Internal_error';

    org_id_ = (form_data_->>'org_id')::int;
    
    IF entry_id_ = 0 THEN
        status = true;
        message = 'success';
        RETURN json_build_object('status', status, 'code', message, 'data', '{}');
    END IF;

    resp_data = array_to_json(array( 
        SELECT json_build_object(	
            'id', exec_master.id,
            'vertical_id', exec_master.vertical_id,
            'service_type_id', exec_master.service_type_id,
            'sku_id', exec_master.sku_id,
            'skill_1', exec_master.skill_1,
            'skill_2', exec_master.skill_2,
            'skill_3', exec_master.skill_3,
            'manpower_1', exec_master.manpower_1,
            'manpower_2', exec_master.manpower_2,
            'manpower_3', exec_master.manpower_3,
            'duration_1', exec_master.duration_1,
            'duration_2', exec_master.duration_2,
            'duration_3', exec_master.duration_3,
            'created_on', (exec_master.c_meta).time,
            'last_u_date', (exec_master.u_meta).time,
            'c_by', exec_master.c_by,
            'u_by', exec_master.u_by,
            'form_data', exec_master.form_data
        )	
        FROM execution_master AS exec_master	          
        WHERE exec_master.id = entry_id_
    ));

    IF json_array_length(resp_data) > 0 THEN 
        status = true;
        message = 'success';
    END IF;

    RETURN json_build_object('status', status, 'code', message, 'data', resp_data->0);

END;
$$;
